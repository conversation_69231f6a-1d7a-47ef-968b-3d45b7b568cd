package com.gumtree.web.seller.page.registration;

import com.gumtree.api.User;
import com.gumtree.api.client.spec.UserApi;
import com.gumtree.config.SellerProperty;
import com.gumtree.seller.domain.user.entity.UserStatus;
import com.gumtree.user.service.ApiResponse;
import com.gumtree.user.service.model.ApiError;
import com.gumtree.user.service.model.AuthenticationProvider;
import com.gumtree.user.service.model.RegisteredUser;
import com.gumtree.user.service.model.UserApiErrorCode;
import com.gumtree.user.service.model.UserApiErrors;
import com.gumtree.user.service.model.UserRegistrationRequest;
import com.gumtree.user.service.support.builder.RegisteredUserBuilder;
import com.gumtree.user.service.support.builder.UserApiErrorsBuilder;
import com.gumtree.user.service.support.builder.UserRegistrationRequestBuilder;
import com.gumtree.user.service.support.factory.ApiErrorFactory;
import com.gumtree.web.common.page.context.GumtreePageContext;
import com.gumtree.web.cookie.cutters.threatmetrix.ThreatMetrixCookie;
import com.gumtree.web.reporting.google.ga.events.impl.UserRegistrationBegin;
import com.gumtree.web.security.exception.FormValidationException;
import com.gumtree.web.security.login.LoginUtils;
import com.gumtree.web.seller.page.BaseSellerControllerTest;
import com.gumtree.web.seller.page.common.model.Page;
import com.gumtree.web.seller.page.registration.model.RegistrationForm;
import com.gumtree.web.seller.page.registration.model.RegistrationModel;
import com.gumtree.web.seller.page.registration.model.RegistrationResult;
import com.gumtree.web.seller.service.threatmetrix.ThreatMetrixService;
import com.gumtree.web.seller.service.user.forgotpassword.PasswordResetService;
import com.gumtree.web.zeno.userregistration.UserRegistrationBeginZenoEvent;
import com.gumtree.web.zeno.userregistration.UserRegistrationFailZenoEvent;
import com.gumtree.web.zeno.userregistration.UserRegistrationSuccessZenoEvent;
import com.gumtree.zeno.core.event.user.sellerside.userregistration.UserRegistrationFail;
import com.netflix.config.ConfigurationManager;
import org.fest.assertions.api.Assertions;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Matchers;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.RedirectView;

import java.time.LocalDate;
import java.util.Properties;

import static org.fest.assertions.api.Assertions.fail;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.contains;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertThrows;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RegistrationPageControllerTest extends BaseSellerControllerTest {
    private static final Long USER_ID = 1L;
    private static final String USER_FIRST_NAME = "john";
    private static final String USER_LAST_NAME = "smith";
    private static final String USER_EMAIL = "<EMAIL>";
    private static final String USER_PASSWORD = "123";
    private static final String USER_PHONE_NUMBER = "7711554486";
    private static final String IP_ADDRESS = "127.0.5";
    private static final String THREATMETRIX_SESSION_ID = "123-456";
    private static final String FIELD_ERROR = "emailAddress";
    private static final String NON_LEGACY_FIELD_ERROR = "username";
    private static final String REGISTER_EMAIL_ERROR_MESSAGE = "registerUser.email.exists";
    private static final String FAILURE_MESSAGE = "FormValidation exception should have been thrown";

    @InjectMocks
    private RegistrationPageController controller;
    @Mock
    private LoginUtils loginUtils;
    @Mock
    private UserApi mockUserApi;
    @Mock
    private ApiResponse<RegisteredUser> registrationResponse;
    @Mock
    private ThreatMetrixCookie threatMetrixCookie;
    @Mock
    private ThreatMetrixService threatMetrixService;

    @Mock
    private PasswordResetService passwordResetService;

    @Mock
    private GumtreePageContext pageContext;

    private RegistrationForm registrationForm;

    static {
        loadProperties();
    }

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        registrationForm = userRegistrationFormFixture();

        when(bushfireApi.create(UserApi.class)).thenReturn(mockUserApi);

        when(userServiceFacade.registerUser(Matchers.any())).thenReturn(registrationResponse);

        autowireAbExperimentsService(controller);
    }

    private static void loadProperties() {
        Properties properties = new Properties();
        properties.setProperty(SellerProperty.FACEBOOK_APP_ID.getPropertyName(), "1234");
        properties.setProperty(SellerProperty.GOOGLE_APP_ID.getPropertyName(), "5678");
        ConfigurationManager.loadProperties(properties);
    }

    @Test
    public void getRegisterPageReturnsRegisterUserBeanWithOptInSetToTrue() {
        // given
        mockSuccessRegistrationResponse();

        // when
        ModelAndView view = controller.viewRegistrationPage(registrationForm, "", request);

        // then
        assertThat(view.getViewName(), equalTo(Page.Registration.getTemplateName()));
        assertThat(registrationForm.isOptInMarketing(), equalTo(true));
        assertThat(registrationForm.getOptInThirdPartyMarketing(), equalTo(true));

        verify(loginUtils).clearNewUserEmailAddressFromSession();
        verify(loginUtils).setNewUserMustLoginToStartPostAdFlow(true);

        RegistrationModel responseModel = (RegistrationModel) view.getModelMap().get("model");
        assertThat(responseModel.getCore().getGaEvents(), contains(UserRegistrationBegin.class.getSimpleName()));
        verify(zenoService, times(1)).logEvent(new UserRegistrationBeginZenoEvent(registrationForm.getEmailAddress()));
    }

    @Test
    public void newRegistrationsReturnsRegistrationResultWhenUserSuccessfullyRegistered() {
        mockSuccessRegistrationResponse();

        RegistrationModel registrationModel = new RegistrationModel();
        registrationModel.setForm(registrationForm);

        mockTmCookieReturningTmxSessionId();
        mockRemoteAddressReturningIPAddress();
        mockGumtreePageContext();

        String expectedResendPath = ResendActivationEmailPageController.EXPERIMENT_PAGE_PATH + "/" + USER_ID;

        // when
        ResponseEntity<RegistrationResult> responseEntity = controller.registerNewUser(registrationModel, request,
                remoteIP, pageContext);

        // then
        assertThat(responseEntity.getStatusCode(), equalTo(HttpStatus.OK));
        RegistrationResult registrationResult = responseEntity.getBody();
        assertThat(registrationResult.getCore().getPage(), equalTo(Page.RegistrationConfirmation));

        assertThat(registrationResult.getResendPath(), equalTo(expectedResendPath));

        verify(userServiceFacade).registerUser(userRegistrationRequestFixture());
        verify(threatMetrixCookie).invalidate();
        verify(zenoService, times(1)).logEvent(new UserRegistrationSuccessZenoEvent(USER_ID, USER_EMAIL));
    }

    @Test
    public void newRegistrationWithErrorsWhenLegacyRegisterUserFailsEmailAddressAlreadyInUseValidationError() {
        // given
        mockFailedRegistrationResponseWithValidationError(ApiErrorFactory.createApiError(FIELD_ERROR, REGISTER_EMAIL_ERROR_MESSAGE));

        RegistrationModel registrationModel = new RegistrationModel();
        registrationModel.setForm(registrationForm);
        registrationForm.setLegacy(true);
        registrationForm.setEmailAddress(USER_EMAIL);

        mockTmCookieReturningTmxSessionId();
        mockRemoteAddressReturningIPAddress();
        mockGumtreePageContext();

        // when
        ResponseEntity<RegistrationResult> responseEntity = controller.registerNewUser(registrationModel, request,
                remoteIP, pageContext);

        // then
        assertThat(responseEntity.getStatusCode(), equalTo(HttpStatus.BAD_REQUEST));
        RegistrationResult registrationResult = responseEntity.getBody();
        assertThat(registrationResult.getCore().getPage(), equalTo(Page.Registration));
        assertTrue(registrationResult.getFormErrors().containsKey(FIELD_ERROR));
    }

    @Test
    public void newRegistrationShouldReturnWithErrorsWhenRegisterUserFailsEmailAddressAlreadyInUseValidationError() {
        // given
        mockFailedRegistrationResponseWithValidationError(ApiErrorFactory.createApiError(FIELD_ERROR, REGISTER_EMAIL_ERROR_MESSAGE));

        RegistrationModel registrationModel = new RegistrationModel();
        registrationModel.setForm(registrationForm);
        registrationForm.setLegacy(false);
        registrationForm.setEmailAddress(USER_EMAIL);

        mockTmCookieReturningTmxSessionId();
        mockRemoteAddressReturningIPAddress();
        mockGumtreePageContext();

        // when
        ResponseEntity<RegistrationResult> responseEntity = controller.registerNewUser(registrationModel, request,
                remoteIP, pageContext);

        // then
        assertThat(responseEntity.getStatusCode(), equalTo(HttpStatus.BAD_REQUEST));
        RegistrationResult registrationResult = responseEntity.getBody();
        assertThat(registrationResult.getCore().getPage(), equalTo(Page.Registration));
        assertTrue(registrationResult.getFormErrors().containsKey(NON_LEGACY_FIELD_ERROR));
    }

    @Test
    public void whenRegisterSuccessfulUserIsRedirectedToConfirmationPage() {
        // given
        mockSuccessRegistrationResponse();

        RegistrationModel registrationModel = new RegistrationModel();
        registrationModel.setForm(registrationForm);

        mockTmCookieReturningTmxSessionId();
        mockRemoteAddressReturningIPAddress();

        // when
        View view = controller.registerUser(registrationModel, request, remoteIP).getView();

        // then
        verify(userServiceFacade).registerUser(userRegistrationRequestFixture());

        // then
        verify(threatMetrixCookie).invalidate();

        // then redirect to confirmation page
        assertTrue(view instanceof RedirectView);
        assertTrue(((RedirectView) view).isRedirectView());
        assertThat(((RedirectView) view).getUrl(), equalTo(ConfirmationPageController.PAGE_PATH));

        // success registration events will be fired on confirmation page load
        verifyZeroInteractions(zenoService);
        // password reset service is not called
        verify(passwordResetService, never()).resetPassword(USER_EMAIL);
    }

    @Test
    public void whenRegisterSuccessfulUserFromIOSIsRedirectedToConfirmationPageWithParameter() {
        // given
        mockSuccessRegistrationResponse();

        RegistrationModel registrationModel = new RegistrationModel();
        registrationModel.setForm(registrationForm);

        mockTmCookieReturningTmxSessionId();
        mockRemoteAddressReturningIPAddress();

        // and
        when(request.getQueryString()).thenReturn("ios=1");

        // when
        View view = controller.registerUser(registrationModel, request, remoteIP).getView();

        // then
        verify(userServiceFacade).registerUser(userRegistrationRequestFixture());

        // then
        verify(threatMetrixCookie).invalidate();

        // then redirect to confirmation page
        assertTrue(view instanceof RedirectView);
        assertTrue(((RedirectView) view).isRedirectView());
        assertThat(((RedirectView) view).getUrl(), equalTo(ConfirmationPageController.PAGE_PATH + "?result=success"));

        // success registration events will be fired on confirmation page load
        verifyZeroInteractions(zenoService);
        // password reset service is not called
        verify(passwordResetService, never()).resetPassword(USER_EMAIL);
    }


    @Test
    public void userReturnedToRegisterPageWithErrorsWhenRegisterUserFailsDueToNonAccountRelatedIssues() {
        // given
        mockFailedRegistrationResponse();

        RegistrationModel incomingModel = new RegistrationModel();
        incomingModel.setForm(registrationForm);

        // and
        mockTmCookieReturningTmxSessionId();
        mockRemoteAddressReturningIPAddress();

        // when
        ModelAndView view = controller.registerUser(incomingModel, request, remoteIP);

        // then
        assertFalse(registrationForm.getFormErrors().isEmpty());
        assertThat(view.getViewName(), equalTo(Page.Registration.getTemplateName()));

        RegistrationModel responseModel = (RegistrationModel) view.getModelMap().get("model");
        assertThat(responseModel.getCore().getGaEvents(), contains(UserRegistrationFail.class.getSimpleName()));
        verify(zenoService, times(1)).logEvent(new UserRegistrationFailZenoEvent(registrationForm.getEmailAddress()));
    }


    @Test
    public void userReturnedToRegisterPageWithErrorsWhenRegisterUserFailsDueToNonAccountRelatedIssuesInWebBffFlow() {
        // given
        mockFailedRegistrationResponse();

        RegistrationModel incomingModel = new RegistrationModel();
        RegistrationForm registrationForm = userRegistrationFormFixture();
        registrationForm.setLegacy(false);
        incomingModel.setForm(registrationForm);

        // and
        mockTmCookieReturningTmxSessionId();
        mockRemoteAddressReturningIPAddress();

        // when
        FormValidationException formValidationException = assertThrows(FormValidationException.class, () -> {
            controller.registerUser(incomingModel, request, remoteIP);
            fail(FAILURE_MESSAGE);
        });

        //then
        assertFalse(formValidationException.getFormErrors().isEmpty());
        verify(zenoService, times(1)).logEvent(new UserRegistrationFailZenoEvent(registrationForm.getEmailAddress()));
    }

    @Test
    public void userReturnedToRegisterPageWithErrorsWhenRegisterUserFailsEmailAddressAlreadyInUseValidationError() {
        // given
        mockFailedRegistrationResponseWithValidationError(ApiErrorFactory.createApiError(FIELD_ERROR, REGISTER_EMAIL_ERROR_MESSAGE));

        RegistrationModel registrationModel = new RegistrationModel();
        registrationModel.setForm(registrationForm);
        registrationForm.setLegacy(false);
        registrationForm.setEmailAddress(USER_EMAIL);

        mockTmCookieReturningTmxSessionId();
        mockRemoteAddressReturningIPAddress();

        // when
        FormValidationException formValidationException = assertThrows(FormValidationException.class, () -> {
            controller.registerUser(registrationModel, request, remoteIP);
            fail(FAILURE_MESSAGE);
        });

        // then
        assertFalse(formValidationException.getFormErrors().isEmpty());
        assertTrue(formValidationException.getFormErrors().containsKey(NON_LEGACY_FIELD_ERROR));
    }

    @Test
    public void userReturnedToRegisterPageWithErrorsWhenLegacyRegisterUserFailsEmailAddressAlreadyInUseValidationError() {
        // given
        mockFailedRegistrationResponseWithValidationError(ApiErrorFactory.createApiError(FIELD_ERROR, REGISTER_EMAIL_ERROR_MESSAGE));

        RegistrationModel registrationModel = new RegistrationModel();
        registrationModel.setForm(registrationForm);
        registrationForm.setLegacy(true);
        registrationForm.setEmailAddress(USER_EMAIL);

        mockTmCookieReturningTmxSessionId();
        mockRemoteAddressReturningIPAddress();

        // when
        controller.registerUser(registrationModel, request, remoteIP);

        // then
        assertFalse(registrationForm.getFormErrors().isEmpty());
        assertTrue(registrationForm.getFormErrors().containsKey(FIELD_ERROR));
    }

    @Test
    public void shouldConvertFormToRegistrationRequest() {
        // given
        RegistrationForm registrationForm = new RegistrationForm();
        registrationForm.setEmailAddress(USER_EMAIL);
        registrationForm.setPassword("gumtree123");
        registrationForm.setFirstName("Michal");
        registrationForm.setLastName("Surn");
        registrationForm.setTelephoneNumber("12345");
        registrationForm.setOptInMarketing(true);
        registrationForm.setOptInThirdPartyMarketing(true);
        registrationForm.setDateOfBirth("2000-02-01");
        registrationForm.setPostcode("postcode");

        // when
        UserRegistrationRequest regRequest = RegistrationPageController.convertToRegistrationRequest(registrationForm, threatMetrixCookie, remoteIP);

        // then
        Assertions.assertThat(regRequest.getUsername()).isEqualTo(USER_EMAIL);
        Assertions.assertThat(regRequest.getFirstName()).isEqualTo("Michal");
        Assertions.assertThat(regRequest.getLastName()).isEqualTo("Surn");
        Assertions.assertThat(regRequest.getAccessToken()).isEqualTo("gumtree123");
        Assertions.assertThat(regRequest.getOptInMarketing()).isEqualTo(true);
        Assertions.assertThat(regRequest.getPhoneNumber()).isEqualTo("12345");
        Assertions.assertThat(regRequest.getAuthenticationProvider()).isEqualTo(AuthenticationProvider.GUMTREE);
        Assertions.assertThat(regRequest.getOptInThirdPartyMarketing()).isEqualTo(true);
        Assertions.assertThat(regRequest.getDateOfBirth()).isEqualTo(LocalDate.of(2000, 2, 1)       );
        Assertions.assertThat(regRequest.getPostcode()).isEqualTo("postcode");

    }

    @Test
    public void shouldConvertFormToRegistrationRequestWithoutDateOfBirth() {
        // given
        RegistrationForm registrationForm = new RegistrationForm();
        registrationForm.setEmailAddress(USER_EMAIL);
        registrationForm.setPassword("gumtree123");
        registrationForm.setFirstName("Michal");
        registrationForm.setLastName("Surn");
        registrationForm.setTelephoneNumber("12345");
        registrationForm.setOptInMarketing(true);
        registrationForm.setOptInThirdPartyMarketing(true);
        // when
        UserRegistrationRequest regRequest = RegistrationPageController.convertToRegistrationRequest(registrationForm, threatMetrixCookie, remoteIP);

        // then
        Assertions.assertThat(regRequest.getUsername()).isEqualTo(USER_EMAIL);
        Assertions.assertThat(regRequest.getFirstName()).isEqualTo("Michal");
        Assertions.assertThat(regRequest.getLastName()).isEqualTo("Surn");
        Assertions.assertThat(regRequest.getAccessToken()).isEqualTo("gumtree123");
        Assertions.assertThat(regRequest.getOptInMarketing()).isEqualTo(true);
        Assertions.assertThat(regRequest.getPhoneNumber()).isEqualTo("12345");
        Assertions.assertThat(regRequest.getAuthenticationProvider()).isEqualTo(AuthenticationProvider.GUMTREE);
        Assertions.assertThat(regRequest.getOptInThirdPartyMarketing()).isEqualTo(true);
        Assertions.assertThat(regRequest.getDateOfBirth()).isEqualTo(null);
    }

    @Test
    public void shouldConvertFormToRegistrationRequestWithoutPostcode() {
        // given
        RegistrationForm registrationForm = new RegistrationForm();
        registrationForm.setEmailAddress(USER_EMAIL);
        registrationForm.setPassword("gumtree123");
        registrationForm.setFirstName("Michal");
        registrationForm.setLastName("Surn");
        registrationForm.setTelephoneNumber("12345");
        registrationForm.setOptInMarketing(true);
        registrationForm.setOptInThirdPartyMarketing(true);
        registrationForm.setDateOfBirth("2000-02-01");

        // when
        UserRegistrationRequest regRequest = RegistrationPageController.convertToRegistrationRequest(registrationForm, threatMetrixCookie, remoteIP);

        // then
        Assertions.assertThat(regRequest.getUsername()).isEqualTo(USER_EMAIL);
        Assertions.assertThat(regRequest.getFirstName()).isEqualTo("Michal");
        Assertions.assertThat(regRequest.getLastName()).isEqualTo("Surn");
        Assertions.assertThat(regRequest.getAccessToken()).isEqualTo("gumtree123");
        Assertions.assertThat(regRequest.getOptInMarketing()).isEqualTo(true);
        Assertions.assertThat(regRequest.getPhoneNumber()).isEqualTo("12345");
        Assertions.assertThat(regRequest.getAuthenticationProvider()).isEqualTo(AuthenticationProvider.GUMTREE);
        Assertions.assertThat(regRequest.getOptInThirdPartyMarketing()).isEqualTo(true);
        Assertions.assertThat(regRequest.getDateOfBirth()).isEqualTo(LocalDate.of(2000, 2, 1)       );
        Assertions.assertThat(regRequest.getPostcode()).isEqualTo(null);
    }

    @Test
    public void shouldReturnPagePath(){
        //given-when
        String pagePath = controller.registerFormAction();

        //then
        assertThat(pagePath, equalTo(RegistrationPageController.PAGE_PATH));
    }

    private void mockSuccessRegistrationResponse() {
        when(registrationResponse.isDefined()).thenReturn(true);
        when(registrationResponse.get()).thenReturn(RegisteredUserBuilder.builder().setUserId(1L).setUserStatus(UserStatus.AWAITING_ACTIVATION.name()).build());
    }

    private void mockFailedRegistrationResponse() {
        when(registrationResponse.isDefined()).thenReturn(false);
        when(registrationResponse.getError()).thenReturn(UserApiErrorsBuilder.builder().withErrorCode(UserApiErrorCode.UNEXPECTED_ERROR).build());
    }

    private void mockFailedRegistrationResponseWithValidationError(ApiError error) {
        UserApiErrors errors = UserApiErrorsBuilder.builder()
                .withErrorCode(UserApiErrorCode.VALIDATION_ERROR)
                .addError(error)
                .build();
        when(registrationResponse.isDefined()).thenReturn(false);
        when(registrationResponse.getError()).thenReturn(errors);
    }

    private RegistrationForm userRegistrationFormFixture() {
        RegistrationForm registrationForm = new RegistrationForm();
        registrationForm.setEmailAddress(USER_EMAIL);
        registrationForm.setPassword(USER_PASSWORD);
        registrationForm.setFirstName(USER_FIRST_NAME);
        registrationForm.setLastName(USER_LAST_NAME);
        registrationForm.setTelephoneNumber(USER_PHONE_NUMBER);
        registrationForm.setOptInMarketing(false);
        registrationForm.setOptInThirdPartyMarketing(true);
        return registrationForm;
    }

    private UserRegistrationRequest userRegistrationRequestFixture() {
        return UserRegistrationRequestBuilder.builder()
                .setUsername(USER_EMAIL)
                .setAccessToken(USER_PASSWORD)
                .setFirstName(USER_FIRST_NAME)
                .setLastName(USER_LAST_NAME)
                .setOptInMarketing(false)
                .setPhoneNumber(USER_PHONE_NUMBER)
                .setAuthenticationProvider(AuthenticationProvider.GUMTREE)
                .setOptInThirdPartyMarketing(true)
                .setThreatMetrixSessionId(THREATMETRIX_SESSION_ID)
                .setIpAddress(IP_ADDRESS)
                .build();
    }

    private void mockTmCookieReturningTmxSessionId() {
        when(cookieResolver.resolve(request, ThreatMetrixCookie.class)).thenReturn(threatMetrixCookie);
        when(threatMetrixCookie.getDefaultValue()).thenReturn(THREATMETRIX_SESSION_ID);
    }

    private void mockRemoteAddressReturningIPAddress() {
        when(remoteIP.getIpAddress()).thenReturn(IP_ADDRESS);
    }

    private void mockGumtreePageContext() {
        User user = new User();
        user.setId(USER_ID);
        user.setEmail(USER_EMAIL);
        when(pageContext.getUser()).thenReturn(user);
    }
}
